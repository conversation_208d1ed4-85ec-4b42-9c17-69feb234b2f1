import { Hash, UrlPath, Uuid } from '../shared/helpers/util'
export interface permissionPositionSettingsUrlProps {
  positionId: string
}
export interface editLeadProps {
  leadId: string
}

// Auth

export const rootPath = '/'
export const signinPath = '/signin'
export const companySignupPath = '/member-signup'
export const signupPath = '/signup'
export const signupSuccess = '/mobile/signup-success'
export const customSignUpPath = '/signup/:path'
export const forgotPasswordPath = '/forgot-password'
export const resetPasswordPath = '/reset-password/:token'

// Team

export const teamPath = '/team'
export const crewPath = '/team/crew'
export const teamMemberPath = '/team/member/:memberUserId/:memberId/:managerId'
export const compensationHistoryPath = '/team/member/compensation/history/:memberId'

// Subcontractor
export const subcontractorPath = '/team/subcontractor'

// Subcontractor
export const helpPath = '/support'

// Dashboard

export const dashboardPath = `/dashboard`

// Time-cards

export const approveTimeCardPath = '/time-cards/approve'
export const deletedTimeCardPath = '/time-cards/deleted'

// Projects

export const projectsPath = `/piecework-projects`

// Settings

export const settingsPath = '/settings'
export const planAndBillingPath = '/plan-and-billing'
export const paySchedulePath = '/settings/pay-schedule'
export const addPaySchedulePath = '/settings/pay-schedule/add'
export const departmentSettingsPath = '/settings/department'
export const adminSettingsPath = '/settings/admin'

export const positionSettingsPath = '/settings/position'
// export const leadsPositionSettingsPath = '/settings/leads/position'
export const permissionPositionSettingsPath = '/settings/position/permission/:positionId'
export const companySettingsPath = '/settings/company'
export const citySettingsPath = '/settings/city'
export const deletedCitySettingsPath = '/settings/city/delete'
export const taskSettingsPath = '/settings/task'
export const leadSourceSettingsPath = '/settings/lead-source'
export const reffererSettingsPath = '/settings/referrer'
export const advertiseSettingsPath = '/settings/advertising-cost'

export const optionsSettingsPath = '/settings/options'
export const editPaySchedulePath = '/settings/pay-schedule/edit/:payScheduleId'
export const deletePaySchedulePath = '/settings/pay-schedule/delete'
export const pieceWorkSettingsSalariedCrewPath = '/settings/piece-work-settings'
export const crmSettingsPath = `/settings/crm`
export const contractsSettingsPath = `/settings/contracts`
export const materialsSettingsPath = `/settings/materials`
export const unitsSettingsPath = `/settings/units`
export const mediaSettingsPath = `/settings/media`
export const inputsSettingsPath = `/settings/inputs`
export const crewPosSettingsPath = `/settings/crew-positions`
export const projectTypeSettingsPath = `/settings/project-types`
export const projectTypeDetailsPath = '/settings/project-types/details/:projectTypeId?'
export const categorySettingsPath = `/settings/category`
export const taxJurisdiction = `/settings/tax-jurisdiction`
export const membersAction = `/settings/members-action`
export const formsBuilder = `/settings/form-builder`
export const formsBuilderCreation = `/settings/form-builder/creation/:builderFormId?`
export const oppFormFill = `/sales/opportunity/:oppId/form/:builderFormId/:formId?/:imageId?`
export const FormFill = `form/:builderFormId/:formId?/:imageId?`
export const otherFormFill = `/forms/form/:builderFormId/:fillFormId?/:imageId?`

export const salesCommission = `/settings/sale-commission`

// Client

export const clientPath = '/client'
export const clientProfilePath = '/client/profile/:clientId/:isDeleted'

// Contact

export const contactPath = '/contact'
export const contactProfilePath = '/contact/profile/:contactId'

// Reports

export const reportsPath = `/reports`
export const actionsPath = `/actions`
export const formsPath = `/forms`
export const weeklyReportsPath = `/reports/weekly-report`
export const salesPersonReportsPath = `/reports/sale-person-report`
export const conversionReportPath = `/reports/conversion-report`
export const customSalesReportsPath = `/reports/custom-sale-report`
export const customCSRReportsPath = `/reports/custom-csr-report`
export const weeklyProductionReport = `/reports/weekly-production-report`
export const weeklyProjectReport = `/reports/weekly-project-report`
export const crewReport = `/reports/crew-report`
export const crewProjectReportPath = `/reports/crewProjectReport`
export const projectReportPath = `/reports/projectReport`
export const crewPayRollPath = `/reports/crew-payroll-report`
export const nonCrewPayRollPath = `/reports/noncrew-payroll-report`
export const marketingReportPath = `/reports/marketing-report`
export const PayRollPath = `/reports/payroll-report`
export const clientValueReport = `/reports/client-value-report`
export const kpiReportPath = `/kpiReport`
export const productionReportPath = `/productionReport`
export const commissionReportPath = `/commissionReport/:startDate/:endDate`
export const runPayRollPath = `/runPayRoll/:paySchId/:startDate/:endDate`

// Leads
export const leadsPath = '/leads'
export const deletedLeads = '/leads/deleted/'
export const newLeadPath = `/leads/newLead/:leadId`
export const inActiveLeads = '/leads/newLead/inactive'
export const lostLeads = '/leads/newLead/lost'
// Sales

export const salesPath = '/sales'
export const deletedSales = '/sales/deleted/'
export const opportunityPath = `/sales/opportunity/:oppId`
export const newProjectPath = `/sales/opportunity/:oppId/new-project/:contactId`
export const editProjectPath = `/sales/opportunity/:oppId/edit-project/:projectId`
export const clientInfoPath = `/sales/opportunity/:oppId/contract/:projectId`
export const orderDetails = `/sales/opportunity/:oppId/orderDetail/:orderId`
export const oldCompletedSales = '/sales/completed'
export const inActiveOperations = '/sales/opportunities/inactive'
export const lostOperations = '/sales/opportunities/lost'
export const salesMediaPath = `/sales/opportunity/:oppId/media`
export const photoReportPathSales = `/sales/opportunity/:oppId/media/photo-report`
export const photoReport = `/photo-report`

// Operations

export const operationsPath = '/operations'
export const newProjectPathOperations = `/operations/opportunity/:oppId/new-project/:contactId`
export const editProjectPathOperations = `/operations/opportunity/:oppId/edit-project/:projectId`
export const clientInfoPathOperations = `/operations/opportunity/:oppId/contract/:projectId`
export const orderDetailsOperations = `/operations/opportunity/:oppId/orderDetail/:orderId`
export const opportunityOperations = `/operations/opportunity/:oppId`
export const oldCompleted = '/operations/completed'
export const opportunityMediaPath = `/operations/opportunity/:oppId/media`
export const photoReportPath = `/operations/opportunity/:oppId/media/photo-report`
export const shareMediaPath = `/media/:linkId`

// Company

export const companyPath = '/company'
// Track

export const trackPath = '/track'

export const mediaGpsPath = '/media-gps'

export const allMediaPath = '/all-media'

// Subscription
export const subscribersPath = '/subscribers'
export const subscriptionPath = '/subscription'
export const plansPath = '/plans'
export const invoicePath = '/invoice/:customerId'

// Profile

export const profilePath = '/profile'

// Clock In/Out

export const clockInOutPath = '/clock-in-out'

// Calculations

export const tasksPath = `/calculations/tasks`
export const packagesPath = `/calculations/packages`

export const invitationPath = '/invitation/:invitationId'
// export const newLeadPath: UrlPath<newLeadProps> = `/new-lead`
export const editLeadPath: UrlPath<editLeadProps> = `/edit-lead/:leadId`

export const networkErrorPath = '/offline'
