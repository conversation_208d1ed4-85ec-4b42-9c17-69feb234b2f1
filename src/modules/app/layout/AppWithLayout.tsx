import { useLocation } from 'react-router-dom'
import { ThemeProvider } from 'styled-components'
import { useEffect } from 'react'
import { useDispatch } from 'react-redux'

import { getTheme, Themes } from '../../../styles/theme'
import { GlobalStyle } from '../../../styles/globalStyle'
import ScrollToTop from '../scrollToTop/ScrollToTop'
import ErrorBoundary from '../errorBoundary/ErrorBoundary'
import AuthLayout from './AuthLayout'
import DashboardLayout from './DashboardLayout'
import { useAppSelector } from '../../../logic/redux/reduxHook'
import { StorageKey } from '../../../shared/helpers/constants'

const AppWithLayout = () => {
  const currentTheme = { ...getTheme(Themes.BASIC), selected: Themes.BASIC }
  const dispatch = useDispatch()
  const { currentCompany } = useAppSelector((state) => state.company)
  const { pathname } = useLocation()

  const planType = currentCompany?.planType

  useEffect(() => {
    if (planType) {
      localStorage.setItem(StorageKey.plan, JSON.stringify(planType))
    }
  }, [planType])

  useEffect(() => {
    const checkForLastPassIcons = () => {
      const inputFields = document.querySelectorAll('input[type="password"], input[type="text"]')

      inputFields.forEach((input) => {
        const lastPassIcon = input?.closest('div')?.querySelector('[data-lastpass-icon-root]')

        if (lastPassIcon) {
          localStorage.setItem(StorageKey.lastPassInstalled, JSON.stringify(true))
        }
      })
    }

    const observer = new MutationObserver(() => {
      checkForLastPassIcons()
    })

    observer.observe(document.body, { childList: true, subtree: true })

    return () => {
      observer.disconnect()
    }
  }, [])

  // Helper function to determine if a route should use AuthLayout
  const isAuthRoute = (path: string) => {
    const authPaths = [
      '/',
      '/signin',
      '/signup',
      '/member-signup',
      '/forgot-password',
      '/invitation',
      '/plans',
      '/mobile/signup-success',
    ]
    return (
      authPaths.includes(path) ||
      path.startsWith('/reset-password/') ||
      path.startsWith('/signup/') ||
      path.includes('/share-media/') ||
      path.startsWith('/media/')
    )
  }

  return (
    <ThemeProvider theme={currentTheme}>
      <GlobalStyle />
      <ScrollToTop />
      <ErrorBoundary>{isAuthRoute(pathname) ? <AuthLayout /> : <DashboardLayout />}</ErrorBoundary>
    </ThemeProvider>
  )
}

export default AppWithLayout
