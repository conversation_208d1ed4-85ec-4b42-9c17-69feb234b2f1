import JSZip from 'jszip'
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom'
import React, { useEffect, useMemo, useRef, useState } from 'react'

import { ShareMediaModal } from './ShareMediaModal'
import UploadFile from './UploadFile'
// @ts-ignore
import convertHeic from 'heic-convert/browser'
import { getMediaSettings, getOpportunityMedia } from '../../logic/apis/media'
import useFetch from '../../logic/apis/useFetch'
import { FlexCol, FlexRow, Text, TooltipContainer } from '../../styles/styled'
import { colors } from '../../styles/theme'
import * as Styled from './style'

import AudioSvg from '../../assets/newIcons/audio.svg'
import DownloadSvg from '../../assets/newIcons/download.svg'
import FilterSvg from '../../assets/newIcons/filter.svg'
import PdfSvg from '../../assets/newIcons/pdf.svg'
import VideoSvg from '../../assets/newIcons/video.svg'
import { dayjsFormat } from '../../shared/helpers/util'
import MediaPreview from './components/MediaPreview'

import PhotoReportIcon from '../../assets/newIcons/photoReport.svg'

import Delete from '../../assets/newIcons/delete.svg'
import EditSvg from '../../assets/newIcons/edit.svg'
import GpsIcon from '../../assets/newIcons/gps.svg'
import SelectAllIcon from '../../assets/newIcons/selectAll.svg'
import ShareIcon from '../../assets/newIcons/share.svg'
import TagIcon from '../../assets/newIcons/tag.svg'
import { mediaGpsPath } from '../../logic/paths'
import { useAppSelector } from '../../logic/redux/reduxHook'
import { FullpageLoader, SLoader } from '../../shared/components/loader/Loader'
import Tag from '../../shared/components/tag'
import { CustomModal } from '../../shared/customModal/CustomModal'
import { DropdownContainer, DropdownContent, FormCont } from '../../shared/dropdownWithCheckboxes/style'
import FormSelect from '../../shared/formSelect/FormSelect'
import { FormAccess, mimeTypesMap } from '../../shared/helpers/constants'
import {
  checkIfFilterIsApplied,
  getSelectedFilters,
  handleFilterChange,
} from '../reports/productionReport/ProductionReport'
import { FilterContainer } from '../reports/productionReport/style'
import CheckboxList from '../track/components/CheckboxList'
import DeleteMediaModal from './DeleteMediaModal'
import MediaTagModal from './MediaTagModal'
import { getThumbnailUrl } from './mediaUtils'

export const getCommonTags = (data: any[]) => {
  if (!data?.length) return []

  const tagLists = data.map(({ tags = [] }) => tags).filter((tags) => tags.length)

  return tagLists.length ? tagLists.reduce((common, tags) => common.filter((tag: any) => tags.includes(tag))) : []
}

const MediaSkeleton = () => (
  <Styled.VideoCard>
    <div style={{ width: '100%', height: '100%', position: 'relative' }}>
      <SLoader
        width={100}
        height={200}
        isPercent
        skeletonStyle={{
          aspectRatio: '1/1',
          borderRadius: '8px',
        }}
      />
    </div>
  </Styled.VideoCard>
)

const MediaGridSkeleton = () => (
  <Styled.VideoGrid>
    {Array.from({ length: 15 }).map((_, index) => (
      <MediaSkeleton key={index} />
    ))}
  </Styled.VideoGrid>
)

const filteredMediaGpsData = (mediaData: any[]) =>
  mediaData.filter(
    (item) =>
      item.mimetype?.includes('image') && item.location?.type === 'Point' && item.location?.coordinates?.length === 2
  )

export const processFile = async (file: any) => {
  if (file.type === 'image/heic' || file.name.toLowerCase().endsWith('.heic')) {
    const buffer = await file.arrayBuffer()
    const convertedBuffer = await convertHeic({
      buffer: Buffer.from(buffer),
      format: 'JPEG',
      quality: 1,
    })

    return new File([convertedBuffer], file.name.replace(/\.heic$/i, '.jpg'), {
      type: 'image/jpeg',
    })
  }
  return file
}

export interface ISelectedMedia {
  _id: string
  tags: string[]
  createdBy: any
  mimetype: string
  url: string
  createdAt: string
  name?: string
}

export const MediaType: Record<string, string> = {
  image: 'Photos',
  video: 'Videos',
  application: 'Documents',
}
const TypeMedia: Record<string, string> = {
  Photos: 'image',
  Videos: 'video',
  Documents: 'application',
}

export const ImageWithFallback = ({
  thumbnail,
  media,
  alt = '',
  className,
  style,
}: {
  thumbnail: string
  media: string
  alt?: string
  className?: string
  style?: React.CSSProperties
}) => {
  const [src, setSrc] = useState(thumbnail)
  const [isValidVideoThumbnail, setIsValidVideoThumbnail] = useState(true)
  const [loaded, setLoaded] = useState(false)

  let isVideoThumbnail = className === 'video'
  const isPdf = className === 'pdf' && isValidVideoThumbnail

  return (
    <>
      {isVideoThumbnail && isValidVideoThumbnail ? (
        <Styled.VideoThumbnailCont>
          <Styled.StyledImage
            src={src}
            alt={alt}
            onError={() => {
              setSrc(media)
              setIsValidVideoThumbnail(false)
            }}
            onLoad={() => {
              setIsValidVideoThumbnail(true)
            }}
            className={isVideoThumbnail && isValidVideoThumbnail ? 'video-thumbnail' : className}
            style={style}
          />

          <img src={VideoSvg} alt="icon" className="play-overlay" />
        </Styled.VideoThumbnailCont>
      ) : (
        <>
          <Styled.StyledImage
            src={src}
            alt={alt}
            onLoad={() => {
              setLoaded(true)
            }}
            onError={() => {
              setSrc(media)
              setIsValidVideoThumbnail(false)
            }}
            className={isVideoThumbnail && isValidVideoThumbnail ? 'video-thumbnail' : isPdf ? 'isPdf' : className}
            style={style}
          />
          {isPdf && loaded && <img src={PdfSvg} alt="icon" className="pdf-overlay" />}
        </>
      )}
    </>
  )
}

export const renderMedia = (
  url: string,
  type: any,
  isPreview = false,
  fullScreen = false,
  memberId?: any,
  oppId?: string,
  media?: any,
  thumbnail?: string
) => {
  switch (type?.split('/')?.[0]) {
    case 'image':
      return (
        <Styled.MediaContainer className={isPreview ? 'preview' : ''}>
          <ImageWithFallback alt="media image" media={url} thumbnail={thumbnail || getThumbnailUrl(url)} />
        </Styled.MediaContainer>
      )
    case 'video':
      return (
        <>
          {fullScreen ? (
            <FlexRow justifyContent="center">
              <Styled.VideoCont>
                <Styled.StyledVideo controls>
                  <source src={url} type="video/mp4" />
                  Your browser does not support the video tag.
                </Styled.StyledVideo>
              </Styled.VideoCont>
            </FlexRow>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : ''}>
              <ImageWithFallback
                alt="video"
                media={VideoSvg}
                thumbnail={thumbnail || getThumbnailUrl(url)}
                className="video"
              />
            </Styled.MediaContainer>
          )}
        </>
      )

    case 'audio':
      return (
        <>
          {fullScreen ? (
            <FlexRow justifyContent="center">
              <Styled.VideoCont>
                <Styled.StyledVideo controls>
                  <source src={url} type="audio/mpeg" />
                  Your browser does not support the audio element.
                </Styled.StyledVideo>
              </Styled.VideoCont>
            </FlexRow>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : ''}>
              <ImageWithFallback
                alt="audio"
                media={AudioSvg}
                thumbnail={thumbnail || getThumbnailUrl(url)}
                className="audio"
              />
            </Styled.MediaContainer>
          )}
        </>
      )

    case 'application':
      return (
        <>
          {fullScreen ? (
            <Styled.IframeCont justifyContent="center">
              <iframe src={url} />
            </Styled.IframeCont>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : 'pdf-cont'}>
              <>
                {media?.builderFormId ? (
                  <>
                    {media?.createdBy?._id === memberId && (
                      <button
                        className="edit-btn"
                        onClick={(e) => {
                          e.stopPropagation()
                          window.location.href = `/sales/opportunity/${oppId!}/form/${media?.builderFormId}/${media?.formId
                            }/${media?._id}`
                        }}
                      >
                        <img src={EditSvg} alt="edit icon" />
                      </button>
                    )}
                  </>
                ) : null}
                <ImageWithFallback
                  alt="pdf"
                  media={PdfSvg}
                  thumbnail={thumbnail || getThumbnailUrl(url)}
                  className="pdf"
                />
              </>
            </Styled.MediaContainer>
          )}
        </>
      )

    case 'audio':
      return (
        <>
          {fullScreen ? (
            <audio controls>
              <source src={url} type={'audio/mp3'} />
            </audio>
          ) : (
            <Styled.MediaContainer className={isPreview ? 'preview' : 'pdf-cont'}>
              <>
                <ImageWithFallback
                  alt="pdf"
                  media={AudioSvg}
                  thumbnail={thumbnail || getThumbnailUrl(url)}
                  className="pdf"
                />
              </>
            </Styled.MediaContainer>
          )}
        </>
      )
    default:
      return <span>Unsupported media type</span>
  }
}

export interface IMedia {
  _id: string
  url: any
  name: any
  mimetype: string
  createdAt: string
  date?: string
  builderFormId?: string
}

const Media = () => {
  const { oppId } = useParams()
  const { company } = useAppSelector((state) => state)
  const memberId = company?.currentMember?._id
  const positionPermissions = company?.positionPermissions

  const { state } = useLocation()
  const [selectedMedia, setSelectedMedia] = useState<any>([])
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  const [selectedMediaData, setSelectedMediaData] = useState<any>([])

  const [mediaData, setMediaData] = useState<any[]>([])
  const [showPreview, setShowPreview] = useState(false)
  const [mediaID, setMediaID] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [bool, setBool] = useState(false)
  const [showTagModal, setShowTagModal] = useState(false)
  const [showFilter, setShowFilter] = useState(false)
  const [fetchBool, setFetchBool] = useState(false)
  const [typeSelected, setTypeSelected] = useState<any>({})
  const [tagSelected, setTagSelected] = useState<any>({})
  const [userSelected, setUserSelected] = useState<any>({})
  const mediaStats = useRef<any>({})
  const [showShareModal, setShowShareModal] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [uploadLoading, setUploadLoading] = useState(false)
  const [selectionView, setSelectionView] = useState(false)
  const [tagBool, setTagBool] = useState(false)
  const navigate = useNavigate()
  const [gpsMediaData, setGpsMediaData] = useState<any[]>([])
  const [downloadLoading, setDownloadLoading] = useState(false)

  const { data: tagsData } = useFetch({
    fetchFn: getMediaSettings,
    refetchTrigger: tagBool,
  })

  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFilter(false)
      }
    }

    if (showFilter) {
      document.addEventListener('mousedown', handleClickOutside)
    } else {
      document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showFilter])

  const { data, loading } = useFetch({
    fetchFn: () => (oppId ? getOpportunityMedia(oppId!) : () => { }),
    refetchTrigger: bool,
  })

  const allMedia = useMemo(() => {
    return mediaData?.[selectedDate as unknown as number]?.map((itm: ISelectedMedia) => ({
      url: itm?.url,
      mimetype: itm?.mimetype,
      user: itm?.createdBy?.name,
      userImage: itm?.createdBy?.userImageUrl?.imageUrl,
      createdAt: itm?.createdAt,
      id: itm?.createdBy?.userImageUrl?._id,
      tags: itm?.tags,
      imageId: itm?._id,
      name: itm?.name,
      originalData: itm,
    }))
  }, [selectedDate, bool, selectedIndex, fetchBool, mediaData])

  const mediaTypeFilters = getSelectedFilters(typeSelected)
  const tagTypeFilters = getSelectedFilters(tagSelected)
  const userTypeFilters = getSelectedFilters(userSelected)

  const filters = {
    mediaTypeFilters,
    tagTypeFilters,
    userTypeFilters,
  }

  const isFilterApplied = checkIfFilterIsApplied(filters)

  const hasMedia = !!data?.images?.length

  useEffect(() => {
    if (data?.images?.length) {
      setFetchBool((p) => !p)

      setGpsMediaData(filteredMediaGpsData(data?.images))
    }
  }, [data?.images])

  const filteredMedia = useMemo(() => {
    return data?.images?.filter((itm: { createdBy: any; mimetype: string; tags: string[] | any[] }) => {
      const mediaTypeMatch = mediaTypeFilters.length === 0 || mediaTypeFilters.includes(itm?.mimetype?.split('/')?.[0])
      const tagMatch = tagTypeFilters.length === 0 || tagTypeFilters.some((tag) => itm?.tags?.includes(tag))
      const userMatch = userTypeFilters.length === 0 || userTypeFilters.includes(itm?.createdBy?.name)

      return mediaTypeMatch && tagMatch && userMatch
    })
  }, [mediaTypeFilters, tagTypeFilters, userTypeFilters])

  const uniqueValues = useMemo(() => {
    const user = new Set()

    const mediaTotal: Record<string, number> = {
      Photos: 0,
      Videos: 0,
      Documents: 0,
    }

    data?.images?.forEach((type: any) => {
      type?.createdBy?.name && user.add(type?.createdBy?.name)
      mediaTotal[MediaType[type?.mimetype?.split('/')?.[0]]] += 1
    })

    mediaStats.current = mediaTotal

    let media = []
    if (mediaStats?.current.Photos) {
      media.push('Photos')
    }
    if (mediaStats?.current.Videos) {
      media.push('Videos')
    }
    if (mediaStats?.current.Documents) {
      media.push('Documents')
    }

    return {
      mediaTypes: media?.map((itm) => ({ name: itm, _id: TypeMedia[itm] })),
      users: [...user]?.map((itm) => ({ name: itm, _id: itm })),
      tags: [...(tagsData?.mediaSetting?.tags || [])]?.map((itm) => ({ name: itm, _id: itm })),
    }
  }, [allMedia, tagsData?.mediaSetting?.tags, data?.images?.length, mediaStats.current])

  useEffect(() => {
    const groupedByDate = (filteredMedia || [])?.reduce((acc: any, item: any) => {
      const date = dayjsFormat(item?.createdAt, 'YYYY-MM-DD')

      if (!acc[date]) {
        acc[date] = []
      }

      acc[date].push({
        ...item,
        date: dayjsFormat(item?.createdAt, 'YYYY-MM-DD'),
      })

      return acc
    }, {})

    setMediaData(groupedByDate)
  }, [filteredMedia?.length, bool, fetchBool])

  const handleCloseClick = (filter: any, fn: Function, arr: any) => {
    fn({
      ...arr,
      [filter]: false,
    })
  }

  const handleMediaSelect = (media: any) => {
    let mediaId = media?._id
    setSelectedMedia((prev: any) => {
      const newSelection = new Set(prev)
      if (newSelection.has(mediaId)) {
        newSelection.delete(mediaId)
        setSelectedMediaData((p: any) => p?.filter((itm: any) => itm?._id !== mediaId))
      } else {
        newSelection.add(mediaId)
        setSelectedMediaData((p: any) => [...p, media?.url])
      }
      return [...newSelection]
    })
  }

  const handleSelectAll = () => {
    const allIds = filteredMedia?.map((media: any) => media?._id)

    if (selectedMedia?.length) {
      setSelectionView(false)
      setSelectedMedia([])
      setSelectedMediaData([])
    } else {
      setSelectionView(true)
      setSelectedMedia(allIds)
      setSelectedMediaData(filteredMedia?.map((media: any) => media?.url))
    }
  }

  const handleDownloadClick = async () => {
    setDownloadLoading(true)
    const poName = state?.poName || `${data?.PO}-${data?.num}`
    const zip = new JSZip()
    const folder = zip.folder(poName)

    await Promise.all(
      selectedMediaData?.map(async (url: string, index: number) => {
        try {
          const res = await fetch(`${url}?nocache=${Date.now()}`)
          const blob = await res.blob()
          const name = url.split('/').pop()?.split('-')?.[1] || `file-${index}`
          folder?.file(name, blob)
        } catch (err) {
          console.error('Error fetching:', url, err)
        }
      })
    )

    const zipBlob = await zip.generateAsync({ type: 'blob' })

    const url = URL.createObjectURL(zipBlob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${poName}-media-files.zip`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    setDownloadLoading(false)
  }

  const allowedMediaTypes = company?.companySettingForAll?.allowedMediaTypes ?? []

  const allowedMIMETypes = allowedMediaTypes.map((ext: string) => mimeTypesMap[ext] || '').filter(Boolean)

  const handleCreatePhotoReport = () => {
    const imageData = selectedMedia?.map((id: string) => {
      return data?.images?.find((itm: any) => itm?._id === id && itm?.mimetype?.includes('image'))
    })

    if (imageData?.length) {
      navigate(`photo-report`, {
        state: {
          oppData: {
            // client-name and address
            PO: `${data?.PO}-${data?.num}`,
            oppId: data?._id,
            clientName: data?.clientName,
            city: data?.city,
            state: data?.state,
            zip: data?.zip,
            street: data?.street,
          },
          imageData: imageData?.filter(Boolean),
        },
      })
    }
  }

  return (
    <Styled.SectionCont>
      <FlexCol>
        <FlexRow margin="0 auto" justifyContent="center">
          <h1>{state?.poName || `${data?.PO || ''}${data?.num ? `-${data?.num}` : ''}`}</h1>
          <Text fontSize="16px" fontWeight="500" color={colors.subRow}>
            {state?.clientName || `${data?.firstName || ''} ${data?.lastName || ''}`}
          </Text>
        </FlexRow>

        {hasMedia ? (
          <FlexRow margin="10px 0 0 0" width="max-content" className="stats">
            <div>
              {mediaStats?.current?.Photos ? (
                <span
                  onClick={() =>
                    setTypeSelected({
                      ...typeSelected,
                      image: true,
                    })
                  }
                >
                  Photos ({mediaStats?.current?.Photos})
                </span>
              ) : null}
              {mediaStats?.current?.Videos ? (
                <span
                  onClick={() =>
                    setTypeSelected({
                      ...typeSelected,
                      video: true,
                    })
                  }
                >
                  &nbsp; Videos ({mediaStats?.current?.Videos})
                </span>
              ) : null}
              {mediaStats?.current?.Documents ? (
                <span
                  onClick={() =>
                    setTypeSelected({
                      ...typeSelected,
                      application: true,
                    })
                  }
                >
                  &nbsp; Documents ({mediaStats?.current?.Documents})
                </span>
              ) : null}
            </div>
          </FlexRow>
        ) : null}

        <FlexRow margin="10px 0 0 0" justifyContent="space-between" className="filter-cont">
          {uploadLoading ? (
            <div>
              <FullpageLoader />
            </div>
          ) : (
            <FlexRow gap="16px" width="max-content" alignItems="flex-start">
              <div style={{ width: '20px' }}>
                <TooltipContainer
                  width="70px"
                  positionLeft="0px"
                  positionBottom="0px"
                  positionLeftDecs="14px"
                  positionBottomDecs="unset"
                  positionTopDecs="-40px"
                  fontSize="14px"
                >
                  <span className="tooltip-content">Upload</span>
                  <UploadFile
                    memberId={memberId!}
                    oppId={oppId!}
                    company={company!}
                    allowedMIMETypes={allowedMIMETypes}
                    setUploadLoading={setUploadLoading}
                    onComplete={() => {
                      setBool((p) => !p)
                    }}
                  />
                </TooltipContainer>
              </div>

              {hasMedia ? (
                <>
                  {gpsMediaData?.length ? (
                    <TooltipContainer
                      width="120px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="10px"
                      positionBottomDecs="unset"
                      positionTopDecs="-40px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">GPS Media</span>
                      <Link
                        to={mediaGpsPath}
                        state={{
                          imageData: gpsMediaData,
                        }}
                      >
                        <img src={GpsIcon} alt="gps icon" style={{ width: '20px', cursor: 'pointer' }} />
                      </Link>
                    </TooltipContainer>
                  ) : null}
                  <TooltipContainer
                    width="120px"
                    positionLeft="0px"
                    positionBottom="0px"
                    positionLeftDecs="10px"
                    positionBottomDecs="unset"
                    positionTopDecs="-40px"
                    fontSize="14px"
                  >
                    <span className="tooltip-content">Select all Media</span>
                    <img
                      src={SelectAllIcon}
                      alt="select icon"
                      style={{ width: '20px', cursor: 'pointer' }}
                      onClick={handleSelectAll}
                    />
                  </TooltipContainer>

                  {/* {selectedMedia?.length !== filteredMedia?.length ? (
                    <TooltipContainer
                      width="120px"
                      positionLeft="0px"
                      positionBottom="0px"
                      positionLeftDecs="10px"
                      positionBottomDecs="unset"
                      positionTopDecs="-40px"
                      fontSize="14px"
                    >
                      <span className="tooltip-content">Select Media</span>

                      <img
                        src={SelectIcon}
                        alt="select All icon"
                        style={{ width: '20px', cursor: 'pointer' }}
                        onClick={() => {
                          setSelectedMedia([])
                          setSelectionView((p) => !p)
                        }}
                      />
                    </TooltipContainer>
                  ) : null} */}
                </>
              ) : null}

              {positionPermissions?.forms ? (
                <FormCont>
                  <FormSelect oppId={oppId!} data={{ whereIsUse: FormAccess.Opportunity }} />
                </FormCont>
              ) : null}

              <TooltipContainer
                width="120px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Delete Media</span>
                {selectedMedia?.length || tagTypeFilters?.length ? (
                  <img
                    src={Delete}
                    alt="select icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      setShowDeleteModal(true)
                    }}
                  />
                ) : null}
              </TooltipContainer>

              <TooltipContainer
                width="120px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Share Media</span>
                {selectedMedia?.length || (isFilterApplied && filteredMedia?.length) ? (
                  <img
                    src={ShareIcon}
                    alt="share icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      setShowShareModal(true)
                    }}
                  />
                ) : null}
              </TooltipContainer>
              <TooltipContainer
                width="150px"
                positionLeft="0px"
                positionBottom="0px"
                positionLeftDecs="10px"
                positionBottomDecs="unset"
                positionTopDecs="-40px"
                fontSize="14px"
              >
                <span className="tooltip-content">Add/Remove Tags</span>
                {selectedMedia?.length || (isFilterApplied && filteredMedia?.length) ? (
                  <img
                    src={TagIcon}
                    alt="tag icon"
                    style={{ width: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      const dataItems = data?.images?.filter((itm: any) => selectedMedia?.includes(itm?._id))
                      const tags = getCommonTags(dataItems)
                      setSelectedTags(tags)
                      setShowTagModal(true)
                    }}
                  />
                ) : null}
              </TooltipContainer>
              {downloadLoading ? (
                <div className="download-loader">
                  <FullpageLoader />
                </div>
              ) : (
                <TooltipContainer
                  width="100px"
                  positionLeft="0px"
                  positionBottom="0px"
                  positionLeftDecs="10px"
                  positionBottomDecs="unset"
                  positionTopDecs="-40px"
                  fontSize="14px"
                >
                  <span className="tooltip-content">Download</span>
                  {selectedMedia?.length || (isFilterApplied && filteredMedia?.length) ? (
                    <img
                      src={DownloadSvg}
                      alt="download icon"
                      style={{
                        width: '20px',
                        height: '20px',
                        cursor: 'pointer',
                      }}
                      onClick={handleDownloadClick}
                    />
                  ) : null}
                </TooltipContainer>
              )}

              {selectedMedia?.length ? (
                <TooltipContainer
                  width="160px"
                  positionLeft="0px"
                  positionBottom="0px"
                  positionLeftDecs="10px"
                  positionBottomDecs="unset"
                  positionTopDecs="-40px"
                  fontSize="14px"
                >
                  <span className="tooltip-content">Create Photo Report</span>

                  <img
                    src={PhotoReportIcon}
                    alt="photo-report-icon"
                    style={{ width: '20px', height: '20px', cursor: 'pointer' }}
                    onClick={() => {
                      // setSelectedMedia([])
                      // setSelectionView((p) => !p)
                      handleCreatePhotoReport()
                    }}
                  />
                </TooltipContainer>
              ) : null}
            </FlexRow>
          )}

          {hasMedia ? (
            <FlexRow width="max-content" className="tag-cont">
              <FlexRow className="tags">
                {mediaTypeFilters?.map((itm) => (
                  <Tag
                    itm={{ name: MediaType[itm], type: 'Type' }}
                    onClose={() => handleCloseClick(itm, setTypeSelected, typeSelected)}
                    showRemoveIcon
                    key={itm}
                  />
                ))}

                {tagTypeFilters?.map((itm) => (
                  <Tag
                    itm={{ name: itm, type: 'Tag' }}
                    onClose={() => handleCloseClick(itm, setTagSelected, tagSelected)}
                    showRemoveIcon
                    key={itm}
                  />
                ))}
                {userTypeFilters?.map((itm) => (
                  <Tag
                    itm={{ name: itm, type: 'User' }}
                    onClose={() => handleCloseClick(itm, setUserSelected, userSelected)}
                    showRemoveIcon
                    key={itm}
                  />
                ))}
              </FlexRow>

              <DropdownContainer ref={dropdownRef} className="filter">
                <img
                  src={FilterSvg}
                  className="filter-icon"
                  alt="filter icon"
                  style={{ width: '20px', cursor: 'pointer' }}
                  onClick={() => setShowFilter((p) => !p)}
                />

                {showFilter ? (
                  <DropdownContent style={{ width: '150px', maxHeight: '500px', overflowY: 'scroll', right: '0px' }}>
                    <h3>Filter by</h3>
                    <FilterContainer
                      margin="10px 0 0 0"
                      gap="0px"
                      justifyContent="flex-start"
                      onClick={(e) => e.stopPropagation()}
                      className="media-filter"
                    >
                      <CheckboxList
                        className="first"
                        title=""
                        data={[]}
                        checkedItems={{}}
                        onSelectionChange={(_val) => {
                          setTypeSelected([])
                          setTagSelected([])
                          setUserSelected([])
                        }}
                        allText="All"
                        isCheckedAll={!mediaTypeFilters?.length && !tagTypeFilters?.length && !userTypeFilters?.length}
                      />
                      <CheckboxList
                        title="Type"
                        className="first"
                        data={uniqueValues.mediaTypes}
                        checkedItems={typeSelected}
                        onSelectionChange={(val) => {
                          handleFilterChange(val, setTypeSelected)
                        }}
                        hideAllCheckbox
                      />
                      <CheckboxList
                        title="People"
                        data={uniqueValues.users}
                        checkedItems={userSelected}
                        onSelectionChange={(val) => {
                          handleFilterChange(val, setUserSelected)
                        }}
                        hideAllCheckbox
                      />
                      <CheckboxList
                        checkedItems={tagSelected}
                        title="Tags"
                        data={uniqueValues.tags}
                        hideAllCheckbox
                        onSelectionChange={(val) => {
                          handleFilterChange(val, setTagSelected)
                        }}
                      />
                    </FilterContainer>
                  </DropdownContent>
                ) : null}
              </DropdownContainer>
            </FlexRow>
          ) : null}
        </FlexRow>
      </FlexCol>

      <Styled.MediaCont>
        {loading ? (
          <MediaGridSkeleton />
        ) : (!data?.images?.length && !loading) || (!loading && !filteredMedia?.length) ? (
          <FlexRow justifyContent="center">
            <h2>No Media Found</h2>
          </FlexRow>
        ) : (
          <>
            {Object?.entries(mediaData)
              ?.sort((a, b) => {
                return new Date(b?.[0])?.getTime() - new Date(a?.[0])?.getTime()
              })
              ?.map(([date, mediaList]) => (
                <div key={date}>
                  <h2>{dayjsFormat(date, 'MM-DD-YYYY')}</h2>

                  <Styled.VideoGrid>
                    {mediaList?.map((media: IMedia, idx: number) => (
                      <Styled.VideoCard
                        key={media?._id}
                        className={selectedMedia.includes(media?._id) ? 'isSelected' : ''}
                      >
                        <div
                          style={{ width: '100%', height: '100%', position: 'relative' }}
                          onClick={() => {
                            // if (selectionView) {
                            //   handleMediaSelect(media)
                            //   return
                            // }
                            setSelectedDate(media?.date!)
                            setSelectedIndex(idx)
                            setShowPreview(true)
                          }}
                          onMouseEnter={() => {
                            // if (media?.mimetype?.split('/')?.[0] === 'image') {
                            setSelectionView(true)
                            setMediaID(media?._id)
                            // }
                          }}
                          onMouseLeave={() => {
                            // if (media?.mimetype?.split('/')?.[0] === 'image') {
                            setMediaID(null)
                            // }
                          }}
                        >
                          {renderMedia(media?.url, media?.mimetype, false, false, memberId, oppId!, media)}

                          {mediaID === media?._id || selectedMedia.includes(media?._id) ? (
                            <input
                              type="checkbox"
                              checked={selectedMedia.includes(media?._id)}
                              onChange={() => handleMediaSelect(media)}
                              style={{
                                cursor: 'pointer',
                              }}
                              // onChange={() => handleMediaSelect(media?._id)}
                              onClick={(e) => e.stopPropagation()}
                            />
                          ) : null}

                          <Styled.Timestamp>
                            <div className="fileName">
                              <TooltipContainer
                                width="220px"
                                positionLeft="0px"
                                positionBottom="0px"
                                positionLeftDecs="100px"
                                positionBottomDecs="20px"
                                positionTopDecs="-40px"
                                fontSize="14px"
                              >
                                <span className="tooltip-content">{media?.name}</span>

                                <span className="name">{media?.name}</span>
                              </TooltipContainer>{' '}
                              <span>{dayjsFormat(media.createdAt, 'hh:mma')}</span>
                            </div>
                          </Styled.Timestamp>
                        </div>
                      </Styled.VideoCard>
                    ))}
                  </Styled.VideoGrid>
                </div>
              ))}
          </>
        )}
      </Styled.MediaCont>

      {showPreview ? (
        <MediaPreview
          selectedIndex={selectedIndex}
          allMedia={allMedia}
          allTags={tagsData?.mediaSetting?.tags}
          isMediaSection
          memberId={memberId}
          setTagBool={setTagBool}
          onSuccess={() => {
            setBool((p) => !p)
            setSelectedIndex(0)
          }}
          onClose={() => {
            setShowPreview(false)
          }}
          info={{
            po: state?.poName || data?.PO,
            clientName: state?.clientName || `${data?.firstName || ''} ${data?.lastName || ''}`,
          }}
        />
      ) : null}

      <CustomModal show={showShareModal} className="media">
        <ShareMediaModal
          selectedMedia={selectedMedia}
          mediaTypeFilters={mediaTypeFilters}
          tagTypeFilters={tagTypeFilters}
          userTypeFilters={userTypeFilters}
          data={data}
          oppId={oppId!}
          setShowShareModal={setShowShareModal}
          showShareModal={showShareModal}
          setSelectedMedia={setSelectedMedia}
        />
      </CustomModal>

      <CustomModal show={showDeleteModal} className="media">
        <DeleteMediaModal
          selectedMedia={selectedMedia}
          onClose={() => {
            setShowDeleteModal(false)
          }}
          onComplete={() => {
            setShowDeleteModal(false)
            setBool((p) => !p)
            setSelectionView(false)
            setSelectedMedia([])
          }}
        />
      </CustomModal>

      <CustomModal show={showTagModal} className="media overflow">
        <MediaTagModal
          onClose={() => {
            setShowTagModal(false)
          }}
          onComplete={() => {
            setBool((p) => !p)
            setShowTagModal(false)
            setSelectedTags([])
            setSelectionView(false)
            setSelectedMedia([])
          }}
          selectedTags={selectedTags}
          setSelectedTags={setSelectedTags}
          tagsData={tagsData}
          oppId={oppId!}
          selectedMedia={selectedMedia}
        />
      </CustomModal>
    </Styled.SectionCont>
  )
}

export default Media
