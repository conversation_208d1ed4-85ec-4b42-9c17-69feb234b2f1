import React, { useState } from 'react'
import * as SharedStyled from '../../styles/styled'
import Button from '../../shared/components/button/Button'
import { dayjsFormat, generateUUID, getEnumValue, isSuccess } from '../../shared/helpers/util'
import { useNavigate } from 'react-router-dom'
import { createComment } from '../../logic/apis/sales'
import { useSelector } from 'react-redux'
import { SmallLoaderCont } from '../../shared/components/loader/style'

interface Opportunity {
  PO: string
  num: string
  oppType: {
    name: string
    _id: string
  }
  stage: {
    stageGroup: string
  }
  _id: string
}

interface InfoOpportunityModalProps {
  onConfirm: () => void
  contactName: string
  opportunities: Opportunity[]
  oppFinalPayload?: any
}

const FoundOpportunity: React.FC<InfoOpportunityModalProps> = ({
  onConfirm,
  contactName,
  opportunities,
  oppFinalPayload,
}) => {
  const navigate = useNavigate()
  const globalSelector = useSelector((state: any) => state)
  const { currentMember } = globalSelector.company
  const [loading, setLoading] = useState(false)
  console.log({ oppFinalPayload }, opportunities)

  const generateOpportunityComment = (values: typeof oppFinalPayload): string => {
    const {
      leadSourceName,
      campaignName,
      oppType,
      oppNotes,
      client,
      street,
      city,
      state,
      zip,
      newLeadDate,
      needsAssessmentDate,
      salesPersonName,
      csrName,
    } = values

    const address = `${street || ''}, ${city || ''}, ${state || ''} ${zip || ''}, USA`.trim()

    const commentLines = [
      `Opportunity Date: ${newLeadDate ? dayjsFormat(newLeadDate, 'MMM DD, YYYY @ hh:mm A') : 'N/A'}`,
      `Opportunity Type: ${oppType || 'N/A'}`,
      `Comments/Notes: ${oppNotes?.trim() || 'None'}`,
      `Contact Name: ${client || 'N/A'}`,
      `Address: ${address !== ', ,  , USA' ? address : 'N/A'}`,
      `Lead Source: ${leadSourceName || 'N/A'}`,
      `Campaign: ${campaignName || 'N/A'}`,
      `Sales Person: ${salesPersonName || 'N/A'}`,
      `CSR: ${csrName || 'N/A'}`,
      `Needs Assessment Date: ${
        needsAssessmentDate ? dayjsFormat(needsAssessmentDate, 'MMM DD, YYYY @ hh:mm A') : 'N/A'
      }`,
    ]

    return commentLines.join('\n')
  }
  console.log({ oppFinalPayload })
  const handleOpportunityClick = async (op: Opportunity) => {
    const comment = oppFinalPayload ? generateOpportunityComment(oppFinalPayload) : 'N/A'
    try {
      const response = await createComment({
        body: comment,
        currDate: new Date().toISOString(),
        // dueDate: new Date().toISOString(),
        memberId: currentMember._id!,
        oppId: op?._id,
        // type: 'comment',
        id: generateUUID()!,
      })
      if (isSuccess(response)) {
        navigate(`/${op?.stage?.stageGroup}/opportunity/${op?._id}`)
      }
    } catch (error) {
      console.log(error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ width: '450px', position: 'relative' }}>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgb(86 113 135 / 70%)',
            backdropFilter: 'blur(2px)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            zIndex: 10,
          }}
        >
          <SmallLoaderCont />
        </div>
      )}
      <SharedStyled.SettingModalContentContainer>
        <SharedStyled.Content maxWidth="512px" width="100%" disableBoxShadow={true} noPadding={true}>
          <SharedStyled.FlexCol gap="18px" alignItems="center">
            <SharedStyled.Text width="100%" fontSize="18px" textAlign="center" fontWeight="600">
              {contactName}
            </SharedStyled.Text>
            <SharedStyled.Text width="100%" fontSize="18px" textAlign="center" fontWeight="600">
              already has {opportunities.length} open {opportunities.length === 1 ? 'opportunity' : 'opportunities'}:
            </SharedStyled.Text>
            {opportunities.map((op, idx) => (
              <SharedStyled.FlexRow
                onClick={() => {
                  setLoading(true)
                  handleOpportunityClick(op)
                }}
                style={{ cursor: 'pointer' }}
                key={idx}
                justifyContent="center"
                gap="8px"
              >
                <SharedStyled.Text fontSize="16px" fontWeight="700" color="#000">
                  {op.PO}-{op.num}
                </SharedStyled.Text>
                <SharedStyled.Text fontSize="16px" fontWeight="700" color="#999">
                  {op?.oppType?.name || '--'}
                </SharedStyled.Text>
              </SharedStyled.FlexRow>
            ))}
            <SharedStyled.Text fontSize="16px" fontWeight="600" textAlign="center" color="#333">
              Click to go to an opportunity above, this will create a comment with all info entered so far
            </SharedStyled.Text>
            <SharedStyled.Text fontSize="16px" fontWeight="600" textAlign="center" color="#333">
              OR
            </SharedStyled.Text>
            <SharedStyled.Text fontSize="16px" fontWeight="600" textAlign="center" color="#333">
              Continue creating this opportunity below
            </SharedStyled.Text>
            <Button type="button" onClick={onConfirm}>
              Continue
            </Button>
          </SharedStyled.FlexCol>
        </SharedStyled.Content>
      </SharedStyled.SettingModalContentContainer>
    </div>
  )
}

export default FoundOpportunity
