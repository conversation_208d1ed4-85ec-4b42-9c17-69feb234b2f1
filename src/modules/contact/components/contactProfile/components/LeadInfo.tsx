import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import {
  dayjsFormat,
  getKeysFromObjects,
  getNameFrom_Id,
  getValueByKeyAndMatch,
  isSuccess,
  notify,
} from '../../../../../shared/helpers/util'

import { IntendWidth } from '../../../style'
import { InputWithValidation } from '../../../../../shared/inputWithValidation/InputWithValidation'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import { SharedDate } from '../../../../../shared/date/SharedDate'
import { SharedDateAndTime } from '../../../../../shared/date/SharedDateAndTime'
import { Form, Formik } from 'formik'
import AutoCompleteIndentation from '../../../../../shared/autoCompleteIndentation/AutoCompleteIndentation'
import { mergeSourceAndCampaignNames } from '../../addNewContactModal/AddNewContactModal'
import {
  getFormattedLeadSrcData,
  getLeadSrcDropdownId,
  getLeadSrcDropdownName,
} from '../../../../leadSource/LeadSource'
import { updateLead } from '../../../../../logic/apis/contact'
import SearchableDropdown from '../../../../../shared/searchableDropdown/SearchableDropdown'
import { fetchSearchReferer, getReferralNameFromID } from '../ContactProfile'
import styled from 'styled-components'

interface LeadDateSectionProps {
  lead: any
  leadIndex: number
  leadSrcData: any[]
  refererres: any[]
  projectTypesDrop: any[]
  toggleCount: (heading: string) => void
  toggleHeading: any
  initFetch: any
  renderInvalidButton: React.ReactNode
  renderLostButton: React.ReactNode
  setShowCreateRuleDialog: (value: React.SetStateAction<boolean>) => void
  setTrackingRuleId: (value: React.SetStateAction<string>) => void
  setSelectedLeadId: (value: React.SetStateAction<string>) => void
  setShowReferrerModal?: (value: React.SetStateAction<boolean>) => void
}

interface TrackingEntry {
  [key: string]: any
  createdAt: string
}
const EntryWrapper = styled.div<{ indent?: boolean }>`
  display: flex;
  flex-direction: column;
  padding-left: ${(props) => (props.indent ? '20px' : '0')};
  gap: 4px;
`

const EntryItem = styled.span`
  font-size: 16px;
  color: #333;

  strong {
    margin-right: 6px;
    color: #111;
  }
`
const LeadInfo: React.FC<LeadDateSectionProps> = ({
  leadIndex,
  lead,
  leadSrcData,
  refererres,
  projectTypesDrop,
  toggleCount,
  toggleHeading,
  initFetch,
  renderInvalidButton,
  renderLostButton,
  setTrackingRuleId,
  setShowCreateRuleDialog,
  setSelectedLeadId,
  setShowReferrerModal,
}) => {
  const [referrerName, setReferrerName] = useState('')

  useEffect(() => {
    if (lead?.referredBy) {
      getReferralNameFromID(lead?.referredBy).then((name) => {
        setReferrerName(name)
      })
    }
  }, [lead?.referredBy])

  const initialValues = {
    newLeadDate: lead ? dayjsFormat(lead?.newLeadDate, 'YYYY-MM-DDTHH:mm') : '',
    leadSourceId: lead ? lead?.leadSourceId : '',
    trackingRuleId: lead ? lead?.trackingRuleId : '',
    leadSourceName:
      lead?.campaignId || lead?.leadSourceId
        ? getLeadSrcDropdownName(lead?.campaignId || lead?.leadSourceId, leadSrcData)?.sourceName
        : '',
    referredBy: lead ? referrerName : '',
    workType: lead ? getValueByKeyAndMatch('name', lead?.workType, 'id', projectTypesDrop) : '',
    invalidLeadReason: lead ? lead?.invalidLeadReason?.reason : '',
    invalidLeadNote: lead ? lead?.invalidLeadReason?.notes : '',
    lostDate: lead ? dayjsFormat(lead?.lostDate, 'YYYY-MM-DDTHH:mm') : '',
    lostReason: lead ? lead?.lostReason?.reason : '',
    lostNote: lead ? lead?.lostReason?.notes : '',
  }
  const [selectedLeadSourceObject, setSelectedLeadSourceObject] = useState()

  const handleInputBlurValue = async (data: any) => {
    try {
      const hasValues = Object.values(data)?.filter(Boolean)
      if (hasValues.length) {
        const res = await updateLead(data, lead?._id)
        if (isSuccess(res)) {
          notify('Lead updated!', 'success')
          initFetch()
          // data.leadSourceName && (lead.leadSourceName = data.leadSourceName)
          // data.referredBy && (lead.referredBy = getValueByKeyAndMatch('name', data?.referredBy, '_id', refererres))
          // data.workType && (lead.workType = getValueByKeyAndMatch('name', data?.workType, 'id', projectTypesDrop))
          // data.newLeadDate && (lead.newLeadDate = dayjsFormat(data?.newLeadDate, 'YYYY-MM-DDTHH:mm'))
        }
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const renderKeyValueList = (entry: TrackingEntry, indent: boolean = false) => {
    const filtered = Object.entries(entry || {})?.filter(([key, value]) => value !== null && value !== '')

    const sorted = [
      ['createdAt', dayjsFormat(entry?.createdAt, 'M/D/YY h:mm A')],
      ...filtered.filter(([key]) => key !== 'createdAt'),
    ]

    return (
      <EntryWrapper indent={indent}>
        {sorted.map(([key, value]) => (
          <EntryItem key={key}>
            <strong>{key}:</strong> {value}
          </EntryItem>
        ))}
      </EntryWrapper>
    )
  }

  return (
    <SharedStyled.FlexCol padding="0 0 0 25px">
      <>
        <SharedStyled.Text
          color={lead?.status === 'active' ? 'green' : 'unset'}
          variant="link"
          fontWeight="700"
          fontSize="18px"
          margin="10px auto 0 0"
        >
          {' '}
          <span onClick={() => toggleCount(`${leadIndex}`)}>
            {!toggleHeading[`${leadIndex}`] ? (
              <span style={{ color: '#000000' }}>&#9654;</span>
            ) : (
              <span style={{ color: '#000000' }}>&#9660;</span>
            )}
            &nbsp; {dayjsFormat(lead?.newLeadDate, 'M/D/YY')} {lead?.status === 'active' ? '(Active)' : ''}
          </span>
        </SharedStyled.Text>

        {toggleHeading[`${leadIndex}`] && (
          <>
            <Formik
              initialValues={initialValues}
              // validationSchema={validationSchema}
              onSubmit={() => {}}
              enableReinitialize={true}
            >
              {({ values, errors, touched, setFieldValue }) => {
                useEffect(() => {
                  if (values.leadSourceName !== '') {
                    const result = getLeadSrcDropdownId(values.leadSourceName || '', leadSrcData)
                    setSelectedLeadSourceObject(result?.leadSourceObject)
                  }
                }, [values.leadSourceName])

                return (
                  <Form style={{ width: '100%' }}>
                    <SharedStyled.FlexCol gap="10px">
                      <SharedDateAndTime
                        value={values?.newLeadDate || ''}
                        labelName="Lead Date"
                        stateName="newLeadDate"
                        error={!!(touched?.newLeadDate && errors?.newLeadDate)}
                        setFieldValue={setFieldValue}
                        onBlur={() => {
                          // if (values?.newLeadDate === lead.newLeadDate) return
                          handleInputBlurValue({ newLeadDate: new Date(values?.newLeadDate) })
                        }}
                      />

                      <div
                        style={{
                          width: '100%',
                        }}
                      >
                        {
                          <>
                            {!!!values?.leadSourceId ? (
                              <SharedStyled.Text
                                color="red"
                                variant="link"
                                textDecoration="underline"
                                onClick={() => {
                                  setShowCreateRuleDialog(true)
                                  setSelectedLeadId(lead?._id)
                                }}
                              >
                                No Lead Source! Click to Create
                              </SharedStyled.Text>
                            ) : values?.trackingRuleId ? (
                              <SharedStyled.Text
                                color="green"
                                variant="link"
                                textDecoration="underline"
                                onClick={() => {
                                  setTrackingRuleId(values?.trackingRuleId)
                                  setSelectedLeadId(lead?._id)
                                  setShowCreateRuleDialog(true)
                                }}
                              >
                                Lead Source Matched! Click to Edit
                              </SharedStyled.Text>
                            ) : (
                              <></>
                            )}
                          </>
                        }
                        {leadSrcData?.length ? (
                          <AutoCompleteIndentation
                            labelName="Lead Source"
                            stateName={`leadSourceName`}
                            isLeadSource
                            dropdownHeight="180px"
                            error={touched.leadSourceName && errors.leadSourceName ? true : false}
                            borderRadius="0px"
                            setFieldValue={setFieldValue}
                            options={mergeSourceAndCampaignNames(leadSrcData)}
                            formatedOptions={getFormattedLeadSrcData(leadSrcData)}
                            value={values.leadSourceName!}
                            setValueOnClick={(val: string) => {
                              setFieldValue('leadSourceName', val)
                            }}
                            className="material-autocomplete"
                            isIndentation={true}
                            onBlur={(name: string) => {
                              // if (name === lead.leadSourceName) {
                              //   return
                              // }

                              const result = getLeadSrcDropdownId(name || '', leadSrcData)
                              const leadSourceId = result?.leadSourceId
                              const campaignId = result?.campaignId || null
                              console.log({ result })
                              const data = {
                                leadSourceId,
                                campaignId,
                              }
                              handleInputBlurValue(data)
                            }}
                          />
                        ) : null}
                        {selectedLeadSourceObject?.code === 'referral' && (
                          <SharedStyled.FlexBox width="95%" justifyContent="end" margin="0 0 0 auto">
                            {/* <CustomSelect
                            labelName="Referrer"
                            stateName="referredBy"
                            error={touched.referredBy && errors.referredBy ? true : false}
                            setFieldValue={setFieldValue}
                            setValue={() => {}}
                            value={values.referredBy}
                            dropDownData={getKeysFromObjects(refererres, 'name')}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                            maxWidth="95%"
                            onBlur={() => {
                              // if (values?.referredBy === lead.referredBy) return
                              const referredBy = getValueByKeyAndMatch('_id', values.referredBy, 'name', refererres)
                              handleInputBlurValue({ referredBy })
                            }}
                          /> */}

                            <SearchableDropdown
                              label="Referrer"
                              placeholder="Type to search"
                              searchFunction={fetchSearchReferer}
                              displayKey={'name'}
                              refererOptions={refererres?.slice(0, 20)}
                              onSelect={(item: any) => {
                                setFieldValue('referredBy', item?.name)

                                const referredBy = item?._id
                                handleInputBlurValue({ referredBy })
                              }}
                              selectedValue={values.referredBy}
                              handleBlur={() => {}}
                              resultExtractor={(res) => res?.data?.data?.referrers || []}
                              showAddOption
                              onAddClick={() => {
                                setShowReferrerModal?.(true)
                              }}
                              showUnKnownOption
                              onUnKnownClick={() => {
                                setFieldValue('referredBy', 'Unknown')

                                handleInputBlurValue({ referredBy: 'unknown' })
                              }}
                            />
                          </SharedStyled.FlexBox>
                        )}
                      </div>

                      <CustomSelect
                        labelName="Work Type"
                        stateName="workType"
                        value={values?.workType || ''}
                        error={!!(touched?.workType && errors?.workType)}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        dropDownData={[...projectTypesDrop.map(({ name }: { name: string }) => name)]}
                        innerHeight="52px"
                        margin="10px 0 0 0"
                        onBlur={() => {
                          // if (values?.workType === lead.workType) return
                          const workType = getValueByKeyAndMatch('id', values.workType, 'name', projectTypesDrop)
                          handleInputBlurValue({ workType })
                        }}
                      />

                      <SharedStyled.FlexRow alignItems="center">
                        <CustomSelect
                          labelName="Invalid Lead Reason"
                          stateName="invalidLeadReason"
                          value={values?.invalidLeadReason || ''}
                          error={!!(touched?.invalidLeadReason && errors?.invalidLeadReason)}
                          setFieldValue={setFieldValue}
                          setValue={() => {}}
                          disabled={true}
                          dropDownData={
                            [
                              // '',
                              // 'Service Not Provided',
                              // 'Outside Service Area',
                              // 'Purchase Material Only',
                              // 'Looking For Work',
                              // 'Unreachable',
                              // 'Spam',
                              // 'Other (Describe in notes)',
                            ]
                          }
                          innerHeight="52px"
                          margin="10px 0 0 0"
                        />
                        <div style={{ marginTop: '10px' }}>{renderInvalidButton}</div>
                      </SharedStyled.FlexRow>

                      <IntendWidth>
                        <InputWithValidation
                          labelName="Invalid Lead Notes"
                          stateName="invalidLeadNote"
                          disabled={true}
                          error={touched.invalidLeadNote && errors.invalidLeadNote ? true : false}
                        />
                      </IntendWidth>

                      <SharedStyled.FlexRow alignItems="center">
                        <SharedDate
                          value={values?.lostDate || ''}
                          labelName="Lost Date"
                          stateName="lostDate"
                          disabled={true}
                          error={!!(touched?.lostDate && errors?.lostDate)}
                          setFieldValue={setFieldValue}
                        />
                        <div style={{ marginTop: '10px' }}>{renderLostButton}</div>
                      </SharedStyled.FlexRow>

                      <CustomSelect
                        labelName="Lost Reason"
                        stateName="lostReason"
                        value={values?.lostReason || ''}
                        error={!!(touched?.lostReason && errors?.lostReason)}
                        setFieldValue={setFieldValue}
                        setValue={() => {}}
                        disabled={true}
                        dropDownData={
                          [
                            // '',
                            // 'Too Expensive',
                            // 'Price Shopping',
                            // 'Went With Other Provider',
                            // 'Discuss With Partner',
                            // 'Wants to Wait',
                            // 'Ghosted',
                            // 'Other (Describe in notes)',
                          ]
                        }
                        innerHeight="52px"
                        margin="10px 0 0 0"
                      />

                      <IntendWidth>
                        <InputWithValidation
                          labelName="Lost Reason Notes"
                          stateName="lostNote"
                          disabled={true}
                          error={touched.lostNote && errors.lostNote ? true : false}
                        />
                      </IntendWidth>
                    </SharedStyled.FlexCol>
                  </Form>
                )
              }}
            </Formik>

            <SharedStyled.Text fontWeight="700" fontSize="18px" margin="10px auto 0 0">
              Tracking
            </SharedStyled.Text>
            {renderKeyValueList(lead?.tracking, true)}
          </>
        )}
      </>
    </SharedStyled.FlexCol>
  )
}

export default LeadInfo
