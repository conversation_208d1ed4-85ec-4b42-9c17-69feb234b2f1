const { TestEnvironment } = require('jest-environment-jsdom')

class CustomTestEnvironment extends TestEnvironment {
  constructor(...args) {
    // Mock canvas before calling super to prevent it from being loaded
    const Module = require('module')
    const originalRequire = Module.prototype.require

    Module.prototype.require = function (id) {
      if (id === 'canvas') {
        return {
          createCanvas: () => ({
            getContext: () => ({
              fillRect: () => {},
              clearRect: () => {},
              getImageData: () => ({ data: new Array(4) }),
              putImageData: () => {},
              createImageData: () => ({ data: new Array(4) }),
              setTransform: () => {},
              drawImage: () => {},
              save: () => {},
              fillText: () => {},
              restore: () => {},
              beginPath: () => {},
              moveTo: () => {},
              lineTo: () => {},
              closePath: () => {},
              stroke: () => {},
              translate: () => {},
              scale: () => {},
              rotate: () => {},
              arc: () => {},
              fill: () => {},
              measureText: () => ({ width: 0 }),
              transform: () => {},
              rect: () => {},
              clip: () => {},
            }),
            toBuffer: () => Buffer.alloc(0),
            toDataURL: () => 'data:image/png;base64,',
            width: 150,
            height: 150,
          }),
          createImageData: () => ({ data: new Array(4) }),
          loadImage: () =>
            Promise.resolve({
              width: 150,
              height: 150,
            }),
        }
      }
      return originalRequire.apply(this, arguments)
    }

    super(...args)

    // Restore original require after jsdom initialization
    Module.prototype.require = originalRequire
  }

  async setup() {
    await super.setup()

    // Additional DOM mocks
    this.global.HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
      fillRect: jest.fn(),
      clearRect: jest.fn(),
      getImageData: jest.fn(() => ({ data: new Array(4) })),
      putImageData: jest.fn(),
      createImageData: jest.fn(() => ({ data: new Array(4) })),
      setTransform: jest.fn(),
      drawImage: jest.fn(),
      save: jest.fn(),
      fillText: jest.fn(),
      restore: jest.fn(),
      beginPath: jest.fn(),
      moveTo: jest.fn(),
      lineTo: jest.fn(),
      closePath: jest.fn(),
      stroke: jest.fn(),
      translate: jest.fn(),
      scale: jest.fn(),
      rotate: jest.fn(),
      arc: jest.fn(),
      fill: jest.fn(),
      measureText: jest.fn(() => ({ width: 0 })),
      transform: jest.fn(),
      rect: jest.fn(),
      clip: jest.fn(),
    }))

    this.global.HTMLCanvasElement.prototype.toDataURL = jest.fn(() => 'data:image/png;base64,')
    this.global.HTMLCanvasElement.prototype.toBlob = jest.fn()
  }
}

module.exports = CustomTestEnvironment
